{"cells": [{"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 25443/25443 [00:32<00:00, 787.82it/s] \n", "100%|██████████| 25443/25443 [00:20<00:00, 1253.39it/s]\n"]}], "source": ["# train_grpo.py\n", "from datasets import load_dataset, load_from_disk\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "from trl import GRPOConfig, GRPOTrainer\n", "from peft import LoraConfig, TaskType, get_peft_model\n", "import multiprocessing\n", "import psutil\n", "import time\n", "import builtins\n", "import io\n", "import sys\n", "import re\n", "import os\n", "import json\n", "import sys\n", "from tqdm import trange\n", "\n", "# This is to avoid the error of too many digits in the input_output\n", "sys.set_int_max_str_digits(0)\n", "\n", "if (os.path.exists('./taco_train')):\n", "    TACO_train = load_from_disk('./taco_train')\n", "else:\n", "    TACO_train = load_dataset(\"BAAI/TACO\", split=\"train\")\n", "    TACO_train.save_to_disk('./taco_train')\n", "\n", "if (os.path.exists('./taco_valid')):\n", "    TACO_valid = load_from_disk('./taco_valid')\n", "else:\n", "    TACO_valid = load_dataset(\"BAAI/TACO\", split=\"test\")\n", "    TACO_valid.save_to_disk('./taco_valid')\n", "\n", "TACO_train = TACO_train \\\n", "    .rename_column('question', 'prompt') \\\n", "    .rename_column('solutions', 'completion')\n", "\n", "TACO_valid = TACO_valid \\\n", "    .rename_column('question', 'prompt') \\\n", "    .rename_column('solutions', 'completion')\n", "#prompt_to_completion_valid = {TACO_valid['prompt'][i]: TACO_valid['completion'][i] for i in range(len(TACO_valid))}\n", "\n", "\n", "prompt_to_completion_train = {TACO_train[i]['prompt']: json.loads(TACO_train[i]['input_output']) for i in trange(len(TACO_train))}\n", "\n", "prompt_to_time = {TACO_train[i]['prompt']: TACO_train[i]['time_limit'] for i in trange(len(TACO_train))}\n", "\n", "#"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Map:   0%|          | 0/25443 [00:00<?, ? examples/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Map: 100%|██████████| 25443/25443 [00:19<00:00, 1323.55 examples/s]\n"]}, {"data": {"text/plain": ["[{'content': 'You are given a problem.\\nThink about the problem and provide your working out.\\nPlace it between <think> and </think>.\\nThen, provide your solution between <solution></solution>',\n", "  'role': 'system'},\n", " {'content': 'This is an interactive problem.\\n\\nIn good old times dwarves tried to develop extrasensory abilities:\\n\\n  * Exactly n dwarves entered completely dark cave. \\n  * Each dwarf received a hat — white or black. While in cave, none of the dwarves was able to see either his own hat or hats of other Dwarves. \\n  * <PERSON>war<PERSON> went out of the cave to the meadow and sat at an arbitrary place one after the other. When a dwarf leaves the cave, he sees the colors of all hats of all dwarves that are seating on the meadow (i.e. left the cave before him). However, he is not able to see the color of his own hat and none of the dwarves can give him this information. \\n  * The task for dwarves was to got diverged into two parts — one with dwarves with white hats and one with black hats. \\n\\n\\n\\nAfter many centuries, dwarves finally managed to select the right place on the meadow without error. Will you be able to repeat their success?\\n\\nYou are asked to successively name n different integer points on the plane. After naming each new point you will be given its color — black or white. Your task is to ensure that the named points can be split by a line in such a way that all points of one color lie on the same side from the line and points of different colors lie on different sides. Moreover, no points can belong to the line. Also, you need to report any such line at the end of the process.\\n\\nIn this problem, the interactor is adaptive — the colors of the points in the tests are not fixed beforehand and the jury program can select them arbitrarily, in particular, depending on your program output.\\n\\nInteraction\\n\\nThe first line of the standard input stream contains an integer n (1 ≤ n ≤ 30) — the number of points your program should name.\\n\\nThen n times your program must print two integer coordinates x and y (0 ≤ x ≤ 109, 0 ≤ y ≤ 109). All points you print must be distinct.\\n\\nIn response to each coordinate pair your program will receive the string \"black\", if the point is black, or \"white\", if the point is white.\\n\\nWhen all n points are processed, you need to print four integers x1, y1, x2 and y2 (0 ≤ x1, y1 ≤ 109, 0 ≤ x2, y2 ≤ 109) — coordinates of points (x1, y1) and (x2, y2), which form a line, which separates n points into black and white. Points (x1, y1) and (x2, y2) should not coincide.\\n\\nHacks\\n\\nTo hack solution use the following format. The first line must contain word \"hack\", the second line should contain the number n and the last line should contain the sequence of 0 and 1 — colors of points, which will be reported to the solution. Unlike the jury tests, colors of points in hacks are always fixed in advance. Of course, the hacked solution wouldn\\'t be able to get the information about the colors in advance.\\n\\nFor example, the hack corresponding to sample test will look like this: \\n    \\n    \\n      \\n    hack  \\n    5  \\n    0 0 1 1 0  \\n    \\n\\nExample\\n\\nInput\\n\\n5\\n<span class=\"tex-span\"></span>\\nblack\\n<span class=\"tex-span\"></span>\\nblack\\n<span class=\"tex-span\"></span>\\nwhite\\n<span class=\"tex-span\"></span>\\nwhite\\n<span class=\"tex-span\"></span>\\nblack\\n\\n\\nOutput\\n\\n<span class=\"tex-span\"></span>\\n0 0\\n<span class=\"tex-span\"></span>\\n3 1\\n<span class=\"tex-span\"></span>\\n2 3\\n<span class=\"tex-span\"></span>\\n4 4\\n<span class=\"tex-span\"></span>\\n0 2\\n<span class=\"tex-span\"></span>\\n1 3 4 1\\n\\nNote\\n\\nIn the sample input and output values are aligned only for simplicity of interpreting them chronologically. In real interaction no \"extra\" line breaks should appear.\\n\\nThe following picture illustrates the first test.\\n\\n<image>',\n", "  'role': 'user'}]"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["test = TACO_train.map(lambda x: {\n", "    \"prompt\" : [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": x[\"prompt\"]},\n", "    ]\n", "})\n", "test[0]['prompt']"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['2.0']\n", "['1.0']\n", "['1.0']\n", "['1']\n", "['1.0']\n", "['2.0']\n", "['1.0']\n", "['0.5']\n", "['1']\n", "['2.0']\n", "['2']\n", "['3.0']\n", "['2.0']\n", "['5.0']\n", "['1.0']\n", "['2.0']\n", "['2.0']\n", "['1.5']\n", "['1.0']\n", "['3.0']\n", "['3.0']\n", "['2.0']\n", "['2.0']\n", "['3.0']\n", "['1.5']\n", "['1']\n", "['1.0']\n", "['1']\n", "['1.0']\n", "['1']\n", "['8.0']\n", "['2']\n", "['3.0']\n", "['2']\n", "['1.0']\n", "['1']\n", "['2']\n", "['2.0']\n", "['4.0']\n", "['2.0']\n", "['1.0']\n", "['4']\n", "['6.0']\n", "['1']\n", "['1']\n", "['2']\n", "['3.0']\n", "['1']\n", "['1.0']\n", "['2.0']\n", "['6.0']\n", "['6.0']\n", "['0.5']\n", "['8.0']\n", "['1']\n", "['1']\n", "['1']\n", "['3.0']\n", "['2']\n", "['4.0']\n", "['0.35122']\n", "['2']\n", "['1.0']\n", "['1']\n", "['1.0']\n", "['2']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['1']\n", "['1.0']\n", "['1.0']\n", "['2.5']\n", "['1']\n", "['2.0']\n", "['1']\n", "['1.0']\n", "['1.0']\n", "['0.235294']\n", "['3.0']\n", "['2.0']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['0.5']\n", "['8.0']\n", "['1']\n", "['1.0']\n", "['1.0']\n", "['1.0']\n", "['2.0']\n", "['2']\n", "['8.0']\n", "['2']\n", "['1.5']\n", "['1.0']\n", "['1.0']\n", "['1']\n", "['0.5']\n", "['1.0']\n", "['2.0']\n", "['1.0']\n", "['2.0']\n", "['2.0']\n", "['1.0']\n", "['4.0']\n", "['2.0']\n", "['1.0']\n", "['1.0']\n", "['2.0']\n", "['2.0']\n", "['1.0']\n", "['1.0']\n", "['3.0']\n", "['1']\n", "['3.0']\n", "['1']\n", "['8.0']\n", "['3.0']\n", "['2']\n", "['2.0']\n", "['3.0']\n", "['1']\n", "['5.0']\n", "['2.0']\n", "['1']\n", "['8.0']\n", "['1']\n", "['0.5']\n", "['8.0']\n", "['2.0']\n", "['1']\n", "['2.0']\n", "['2.0']\n", "['2.0']\n", "['2.0']\n", "['2']\n", "['2.0']\n", "['8.0']\n", "['2.0']\n", "['1.0']\n", "['1']\n", "['8.0']\n", "['1.0']\n", "['1.0']\n", "['1.0']\n", "['2.0']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['1.0']\n", "['1']\n", "['2.0']\n", "['3.0']\n", "['1']\n", "['1.0']\n", "['2.0']\n", "['8.0']\n", "['1']\n", "['1.0']\n", "['8.0']\n", "['2.0']\n", "['4.0']\n", "['1']\n", "['2.0']\n", "['1']\n", "['1.0']\n", "['2.0']\n", "['1']\n", "['1']\n", "['0.5']\n", "['2.0']\n", "['3.0']\n", "['2.0']\n", "['1.0']\n", "['2.0']\n", "['1.0']\n", "['2.0']\n", "['1']\n", "['2']\n", "['1']\n", "['1']\n", "['2.0']\n", "['4.0']\n", "['2.0']\n", "['1']\n", "['2.495']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['8.0']\n", "['2.0']\n", "['2.0']\n", "['1.0']\n", "['0.5']\n", "['1']\n", "['1.0']\n", "['1.0']\n", "['1.0']\n", "['4.0']\n", "['8.0']\n", "['1.0']\n", "['2']\n", "['1']\n", "['1']\n", "['1']\n", "['1']\n", "['2']\n", "['3.0']\n", "['1.0']\n", "['1']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['7.0']\n", "['1']\n", "['1']\n", "['4.0']\n", "['1.0']\n", "['2']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['2.0']\n", "['4.0']\n", "['2.0']\n", "['2.5']\n", "['1.0']\n", "['1']\n", "['8.0']\n", "['2.0']\n", "['1.0']\n", "['6.0']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['3.0']\n", "['5.0']\n", "['1.5']\n", "['0.48995']\n", "['2.0']\n", "['2.0']\n", "['8.0']\n", "['1.0']\n", "['1']\n", "['1.0']\n", "['2']\n", "['2.0']\n", "['1.0']\n", "['2.0']\n", "['1']\n", "['1.0']\n", "['1.0']\n", "['0.5']\n", "['2.0']\n", "['2.0']\n", "['1.0']\n", "['3.0']\n", "['1']\n", "['8.0']\n", "['0.5']\n", "['2.0']\n", "['0.5']\n", "['2.0']\n", "['4.0']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['8.0']\n", "['2']\n", "['3.0']\n", "['2.0']\n", "['1.0']\n", "['8.0']\n", "['2']\n", "['3.0']\n", "['1']\n", "['1']\n", "['2.0']\n", "['0.5']\n", "['1.0']\n", "['2.0']\n", "['1']\n", "['1']\n", "['1.0']\n", "['2.0']\n", "['2.0']\n", "['2.0']\n", "['4.0']\n", "['8.0']\n", "['0.5']\n", "['3.0']\n", "['2']\n", "['0.261261']\n", "['1.0']\n", "['1.5']\n", "['1.0']\n", "['2']\n", "['1.0']\n", "['2']\n", "['2.0']\n", "['2.0']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['2.0']\n", "['1']\n", "['5.0']\n", "['1']\n", "['1.0']\n", "['2.0']\n", "['2.0']\n", "['3.0']\n", "['3.0']\n", "['0.83']\n", "['1']\n", "['1.0']\n", "['2.0']\n", "['0.5']\n", "['1']\n", "['2.0']\n", "['2.0']\n", "['2.0']\n", "['8.0']\n", "['5.0']\n", "['1.0']\n", "['2.0']\n", "['1']\n", "['2.0']\n", "['2.0']\n", "['3.0']\n", "['2.2']\n", "['4.0']\n", "['2.0']\n", "['8.0']\n", "['5.0']\n", "['1']\n", "['5.0']\n", "['8.0']\n", "['1']\n", "['1.0']\n", "['2']\n", "['2.0']\n", "['3.0']\n", "['2']\n", "['1.0']\n", "['4.0']\n", "['8.0']\n", "['2.0']\n", "['1.5']\n", "['2.0']\n", "['0.5 - 15']\n", "['1']\n", "['8.0']\n", "['5.0']\n", "['1']\n", "['2.0']\n", "['2']\n", "['1.0']\n", "['1']\n", "['1']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['1']\n", "['2']\n", "['1.0']\n", "['2.0']\n", "['2.0']\n", "['2']\n", "['4.0']\n", "['2.0']\n", "['5']\n", "['2.0']\n", "['1']\n", "['5.0']\n", "['1']\n", "['2.0']\n", "['3.0']\n", "['2']\n", "['2']\n", "['8.0']\n", "['5.0']\n", "['1.0']\n", "['2.0']\n", "['1.0']\n", "['1.0']\n", "['2.0']\n", "['1']\n", "['1']\n", "['1']\n", "['8.0']\n", "['1']\n", "['5.0']\n", "['2.0']\n", "['3.0']\n", "['8.0']\n", "['2.0']\n", "['1.0']\n", "['2.5']\n", "['2.0']\n", "['2']\n", "['2.0']\n", "['1']\n", "['2']\n", "['1']\n", "['1']\n", "['0.508134']\n", "['3.0']\n", "['1']\n", "['2.0']\n", "['2.0']\n", "['1']\n", "['2.0']\n", "['4.0']\n", "['4']\n", "['1']\n", "['4.0']\n", "['2.0']\n"]}, {"data": {"text/plain": ["'6.0'"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["for i in range(1000):\n", "    if TACO_train[i]['time_limit'] != None:\n", "        print(re.compile(\"(.*) second\").findall(TACO_train[i]['time_limit']))\n", "\n", "TACO_train[100]['time_limit'][:-8]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["#this is the code that will be used to test the code\n", "def test_code(code, cases, ex_out, time_limit):\n", "    score = 0\n", "    correct_cases = 0\n", "\n", "    def run_code(code, case_input, output_queue):\n", "        # Mock input\n", "        builtins.input = lambda: case_input\n", "\n", "        # Capture output\n", "        buf = io.StringIO()\n", "        sys.stdout = buf\n", "        try:\n", "            exec(code)\n", "        except Exception as e:\n", "            output_queue.put((\"ERROR\\n\", 0))\n", "            return\n", "        finally:\n", "            sys.stdout = sys.__stdout__\n", "\n", "        output_queue.put((buf.getvalue(), 0))  # memory will be added later\n", "\n", "    for i in range(len(cases)):\n", "        output_queue = multiprocessing.Queue()\n", "        p = multiprocessing.Process(target=run_code, args=(code, cases[i], output_queue))\n", "        p.start()\n", "        proc = psutil.Process(p.pid)\n", "\n", "        max_mem = 0\n", "        start_time = time.time()\n", "        first_mem = proc.memory_info().rss / 1024\n", "        while p.is_alive() and time.time() - start_time < time_limit:\n", "            try:\n", "                mem = proc.memory_info().rss / 1024  # in KB\n", "                max_mem = max(max_mem, mem)\n", "            except psutil.NoSuchProcess:\n", "                break\n", "            time.sleep(0.05)\n", "\n", "        if p.is_alive():\n", "            p.terminate()\n", "            out = \"TIME LIMIT EXCEEDED\\n\"\n", "            score -= 1 / len(cases) * 10\n", "        else:\n", "            try:\n", "                out, _ = output_queue.get(timeout=1)\n", "            except:\n", "                out = \"ERROR\\n\"\n", "                score -= 1 / len(cases) * 50\n", "\n", "        #print(f\"Case {i+1}: Memory used ≈ {int(max_mem)} KB\")\n", "\n", "        if out == ex_out[i]:\n", "            correct_cases += 1\n", "    score += correct_cases / len(cases) * 100\n", "    if correct_cases == len(cases):\n", "        score += 25\n", "    return score\n", "\n", "def extract_tags(completion, tag_start, tag_end):\n", "    return re.compile(f\"{tag_start}(.*?){tag_end}\").find(completion)\n", "\n", "def reward_check(completions, **kwargs):\n", "    #completions is the code???\n", "    rewards=[]\n", "    for completion in completions:\n", "        code = extract_tags(completion, \"<solution>\", \"</solution>\")\n", "        time_limit = prompt_to_time[kwargs['prompt']]\n", "        cases = prompt_to_completion_train[kwargs['prompt']]['inputs']\n", "        ex_out = prompt_to_completion_train[kwargs['prompt']]['outputs']\n", "        reward = test_code(code, cases, ex_out, time_limit)\n", "        rewards.append(reward)\n", "    return rewards"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["reasoning_start = \"<think>\" \n", "reasoning_end   = \"</think>\"\n", "solution_start  = \"<solution>\"\n", "solution_end    = \"</solution>\"\n", "\n", "system_prompt = \\\n", "f\"\"\"You are given a problem.\n", "Think about the problem and provide your working out.\n", "Place it between {reasoning_start} and {reasoning_end}.\n", "Then, provide your solution between {solution_start}{solution_end}\"\"\""]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["peft_config = LoraConfig(task_type=TaskType.CAUSAL_LM, inference_mode=False, r=8, lora_alpha=32, lora_dropout=0.1)\n", "training_args = GRPOConfig(output_dir=\"Qwen2-0.5B-GRPO\", logging_steps=10, per_device_train_batch_size=24)\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen2-0.5B-Instruct\")\n", "model = AutoModelForCausalLM.from_pretrained(\"Qwen/Qwen2-0.5B-Instruct\")\n", "model = get_peft_model(model, peft_config)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["chat_template = \\\n", "    \"{% if messages[0]['role'] == 'system' %}\"\\\n", "        \"{{ messages[0]['content'] + eos_token }}\"\\\n", "        \"{% set loop_messages = messages[1:] %}\"\\\n", "    \"{% else %}\"\\\n", "        \"{{ '{system_prompt}' + eos_token }}\"\\\n", "        \"{% set loop_messages = messages %}\"\\\n", "    \"{% endif %}\"\\\n", "    \"{% for message in loop_messages %}\"\\\n", "        \"{% if message['role'] == 'user' %}\"\\\n", "            \"{{ message['content'] }}\"\\\n", "        \"{% elif message['role'] == 'assistant' %}\"\\\n", "            \"{{ message['content'] + eos_token }}\"\\\n", "        \"{% endif %}\"\\\n", "    \"{% endfor %}\"\\\n", "    \"{% if add_generation_prompt %}{{ '{reasoning_start}' }}\"\\\n", "    \"{% endif %}\"\n", "\n", "# Replace with out specific template:\n", "chat_template = chat_template\\\n", "    .replace(\"'{system_prompt}'\",   f\"'{system_prompt}'\")\\\n", "    .replace(\"'{reasoning_start}'\", f\"'{reasoning_start}'\")\n", "tokenizer.chat_template = chat_template"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"You are given a problem.\\nThink about the problem and provide your working out.\\nPlace it between <think> and </think>.\\nThen, provide your solution between <solution></solution><|im_end|>Hello, how are you?I'm doing well, thank you!<|im_end|>What is the capital of France?<think>\""]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.apply_chat_template([\n", "    {\"role\": \"user\", \"content\": \"Hello, how are you?\"},\n", "    {\"role\": \"assistant\", \"content\": \"I'm doing well, thank you!\"},\n", "    {\"role\": \"user\", \"content\": \"What is the capital of France?\"},\n", "], tokenize = False, add_generation_prompt = True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "0", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[41]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[43mprompt_to_completion_train\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m)\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 0"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[42]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;43mprint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mprompt_to_completion_train\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": []}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Map: 100%|██████████| 25443/25443 [00:35<00:00, 723.01 examples/s] \n"]}, {"data": {"text/plain": ["{'prompt': [{'content': 'You are given a problem.\\nThink about the problem and provide your working out.\\nPlace it between <think> and </think>.\\nThen, provide your solution between <solution></solution>',\n", "   'role': 'system'},\n", "  {'content': 'This is an interactive problem.\\n\\nIn good old times dwarves tried to develop extrasensory abilities:\\n\\n  * Exactly n dwarves entered completely dark cave. \\n  * Each dwarf received a hat — white or black. While in cave, none of the dwarves was able to see either his own hat or hats of other Dwarves. \\n  * <PERSON>war<PERSON> went out of the cave to the meadow and sat at an arbitrary place one after the other. When a dwarf leaves the cave, he sees the colors of all hats of all dwarves that are seating on the meadow (i.e. left the cave before him). However, he is not able to see the color of his own hat and none of the dwarves can give him this information. \\n  * The task for dwarves was to got diverged into two parts — one with dwarves with white hats and one with black hats. \\n\\n\\n\\nAfter many centuries, dwarves finally managed to select the right place on the meadow without error. Will you be able to repeat their success?\\n\\nYou are asked to successively name n different integer points on the plane. After naming each new point you will be given its color — black or white. Your task is to ensure that the named points can be split by a line in such a way that all points of one color lie on the same side from the line and points of different colors lie on different sides. Moreover, no points can belong to the line. Also, you need to report any such line at the end of the process.\\n\\nIn this problem, the interactor is adaptive — the colors of the points in the tests are not fixed beforehand and the jury program can select them arbitrarily, in particular, depending on your program output.\\n\\nInteraction\\n\\nThe first line of the standard input stream contains an integer n (1 ≤ n ≤ 30) — the number of points your program should name.\\n\\nThen n times your program must print two integer coordinates x and y (0 ≤ x ≤ 109, 0 ≤ y ≤ 109). All points you print must be distinct.\\n\\nIn response to each coordinate pair your program will receive the string \"black\", if the point is black, or \"white\", if the point is white.\\n\\nWhen all n points are processed, you need to print four integers x1, y1, x2 and y2 (0 ≤ x1, y1 ≤ 109, 0 ≤ x2, y2 ≤ 109) — coordinates of points (x1, y1) and (x2, y2), which form a line, which separates n points into black and white. Points (x1, y1) and (x2, y2) should not coincide.\\n\\nHacks\\n\\nTo hack solution use the following format. The first line must contain word \"hack\", the second line should contain the number n and the last line should contain the sequence of 0 and 1 — colors of points, which will be reported to the solution. Unlike the jury tests, colors of points in hacks are always fixed in advance. Of course, the hacked solution wouldn\\'t be able to get the information about the colors in advance.\\n\\nFor example, the hack corresponding to sample test will look like this: \\n    \\n    \\n      \\n    hack  \\n    5  \\n    0 0 1 1 0  \\n    \\n\\nExample\\n\\nInput\\n\\n5\\n<span class=\"tex-span\"></span>\\nblack\\n<span class=\"tex-span\"></span>\\nblack\\n<span class=\"tex-span\"></span>\\nwhite\\n<span class=\"tex-span\"></span>\\nwhite\\n<span class=\"tex-span\"></span>\\nblack\\n\\n\\nOutput\\n\\n<span class=\"tex-span\"></span>\\n0 0\\n<span class=\"tex-span\"></span>\\n3 1\\n<span class=\"tex-span\"></span>\\n2 3\\n<span class=\"tex-span\"></span>\\n4 4\\n<span class=\"tex-span\"></span>\\n0 2\\n<span class=\"tex-span\"></span>\\n1 3 4 1\\n\\nNote\\n\\nIn the sample input and output values are aligned only for simplicity of interpreting them chronologically. In real interaction no \"extra\" line breaks should appear.\\n\\nThe following picture illustrates the first test.\\n\\n<image>',\n", "   'role': 'user'}],\n", " 'completion': '[]',\n", " 'starter_code': '',\n", " 'input_output': '{\"inputs\": [\"hack\\\\n30\\\\n1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\\\\n\", \"random\\\\n22\\\\n2\\\\n\", \"random\\\\n20\\\\n11\\\\n\", \"random\\\\n10\\\\n1\\\\n\", \"random\\\\n20\\\\n12\\\\n\", \"random\\\\n30\\\\n14\\\\n\", \"random\\\\n23\\\\n1\\\\n\", \"predefined\\\\n15\\\\n1 1 1 1 1 1 1 1 1 1 1 1 1 1 0\\\\n\", \"random\\\\n21\\\\n2\\\\n\", \"random\\\\n22\\\\n1\\\\n\", \"random\\\\n28\\\\n1\\\\n\", \"random\\\\n28\\\\n2\\\\n\", \"chess\\\\n10\\\\n\", \"chess\\\\n11\\\\n\", \"random\\\\n25\\\\n2\\\\n\", \"random\\\\n24\\\\n1\\\\n\", \"random\\\\n13\\\\n1\\\\n\", \"chess\\\\n19\\\\n\", \"predefined\\\\n15\\\\n1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\\\\n\", \"random\\\\n29\\\\n1\\\\n\", \"random\\\\n20\\\\n1\\\\n\", \"random\\\\n26\\\\n1\\\\n\", \"predefined\\\\n2\\\\n0 1\\\\n\", \"random\\\\n16\\\\n1\\\\n\", \"random\\\\n15\\\\n1\\\\n\", \"random\\\\n8\\\\n1\\\\n\", \"random\\\\n19\\\\n1\\\\n\", \"random\\\\n21\\\\n1\\\\n\", \"random\\\\n27\\\\n1\\\\n\", \"predefined\\\\n2\\\\n0 0\\\\n\", \"random\\\\n2\\\\n1\\\\n\", \"predefined\\\\n15\\\\n0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\\\\n\", \"random\\\\n3\\\\n1\\\\n\", \"predefined\\\\n15\\\\n0 1 0 1 0 1 0 1 0 1 0 1 0 1 0\\\\n\", \"predefined\\\\n29\\\\n0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\\\\n\", \"hack\\\\n30\\\\n0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\\\\n\", \"predefined\\\\n29\\\\n1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0\\\\n\", \"predefined\\\\n30\\\\n0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\\\\n\", \"random\\\\n23\\\\n2\\\\n\", \"random\\\\n29\\\\n2\\\\n\", \"random\\\\n30\\\\n13\\\\n\", \"random\\\\n9\\\\n1\\\\n\", \"predefined\\\\n16\\\\n1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0\\\\n\", \"predefined\\\\n8\\\\n1 1 1 1 1 1 1 0\\\\n\", \"random\\\\n30\\\\n2\\\\n\", \"hack\\\\n5\\\\n0 0 1 1 0\\\\n\", \"random\\\\n25\\\\n1\\\\n\", \"random\\\\n14\\\\n1\\\\n\", \"predefined\\\\n8\\\\n1 1 1 1 1 1 1 1\\\\n\", \"random\\\\n12\\\\n1\\\\n\", \"random\\\\n30\\\\n1\\\\n\", \"random\\\\n17\\\\n1\\\\n\", \"random\\\\n26\\\\n2\\\\n\", \"chess\\\\n30\\\\n\", \"random\\\\n18\\\\n1\\\\n\", \"predefined\\\\n29\\\\n0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0\\\\n\", \"chess\\\\n20\\\\n\", \"predefined\\\\n8\\\\n0 0 0 0 0 0 0 0\\\\n\", \"random\\\\n27\\\\n2\\\\n\", \"random\\\\n5\\\\n1\\\\n\", \"predefined\\\\n29\\\\n1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\\\\n\", \"random\\\\n24\\\\n2\\\\n\", \"random\\\\n11\\\\n1\\\\n\", \"random\\\\n6\\\\n1\\\\n\", \"random\\\\n4\\\\n1\\\\n\", \"predefined\\\\n30\\\\n1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\\\\n\", \"predefined\\\\n1\\\\n0\\\\n\", \"predefined\\\\n5\\\\n0 1 0 1 1\\\\n\", \"chess\\\\n29\\\\n\", \"random\\\\n7\\\\n1\\\\n\", \"random\\\\n1\\\\n1\\\\n\", \"predefined\\\\n8\\\\n0 1 0 1 0 1 0 1\\\\n\", \"predefined\\\\n1\\\\n1\\\\n\", \"hack\\\\n30\\\\n1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1\\\\n\", \"random\\\\n7\\\\n2\\\\n\", \"modnar\\\\n20\\\\n11\\\\n\", \"random\\\\n10\\\\n2\\\\n\", \"sandom\\\\n20\\\\n12\\\\n\", \"random\\\\n30\\\\n25\\\\n\", \"random\\\\n34\\\\n1\\\\n\", \"oredefined\\\\n15\\\\n1 1 1 1 1 1 1 1 1 1 1 1 1 1 0\\\\n\", \"radnom\\\\n21\\\\n2\\\\n\", \"random\\\\n21\\\\n0\\\\n\", \"rbndom\\\\n28\\\\n1\\\\n\", \"random\\\\n13\\\\n2\\\\n\", \"cgess\\\\n10\\\\n\", \"ciess\\\\n11\\\\n\", \"random\\\\n2\\\\n2\\\\n\", \"random\\\\n35\\\\n1\\\\n\", \"random\\\\n13\\\\n0\\\\n\", \"shesc\\\\n19\\\\n\", \"predefined\\\\n15\\\\n1 1 2 1 1 1 1 1 1 1 1 1 1 1 1\\\\n\", \"modnar\\\\n29\\\\n2\\\\n\", \"modnar\\\\n15\\\\n1\\\\n\", \"random\\\\n26\\\\n0\\\\n\", \"predefinec\\\\n2\\\\n0 1\\\\n\", \"random\\\\n8\\\\n2\\\\n\", \"random\\\\n15\\\\n0\\\\n\", \"rnadom\\\\n8\\\\n1\\\\n\", \"rbndom\\\\n19\\\\n1\\\\n\", \"sandom\\\\n21\\\\n1\\\\n\", \"r`ndom\\\\n27\\\\n1\\\\n\", \"denifederp\\\\n2\\\\n0 1\\\\n\", \"rbndom\\\\n3\\\\n1\\\\n\", \"predefined\\\\n15\\\\n0 0 0 0 0 0 0 0 0 0 -1 0 0 0 0\\\\n\", \"random\\\\n3\\\\n0\\\\n\", \"predefined\\\\n15\\\\n0 1 0 1 0 1 0 1 0 0 0 1 0 1 0\\\\n\", \"predefined\\\\n3\\\\n0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\\\\n\", \"hack\\\\n30\\\\n0 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\\\\n\", \"predefined\\\\n29\\\\n1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0\\\\n\", \"predefined\\\\n30\\\\n0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\\\\n\", \"random\\\\n15\\\\n2\\\\n\", \"random\\\\n58\\\\n2\\\\n\", \"random\\\\n30\\\\n0\\\\n\", \"raodom\\\\n9\\\\n1\\\\n\", \"predefined\\\\n16\\\\n1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 0\\\\n\", \"predefined\\\\n8\\\\n0 1 1 1 1 1 1 0\\\\n\", \"r`ndom\\\\n30\\\\n2\\\\n\", \"hack\\\\n5\\\\n0 1 1 1 0\\\\n\", \"modnar\\\\n2\\\\n1\\\\n\", \"random\\\\n14\\\\n2\\\\n\", \"precefined\\\\n8\\\\n1 1 1 1 1 1 1 1\\\\n\", \"r`ndom\\\\n14\\\\n1\\\\n\", \"random\\\\n30\\\\n4\\\\n\", \"raneom\\\\n17\\\\n1\\\\n\", \"randon\\\\n26\\\\n2\\\\n\", \"ssehc\\\\n30\\\\n\", \"random\\\\n31\\\\n1\\\\n\", \"predefined\\\\n29\\\\n0 1 0 1 0 1 0 1 0 1 0 0 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0\\\\n\", \"chess\\\\n13\\\\n\", \"denifederp\\\\n8\\\\n0 0 0 0 0 0 0 0\\\\n\", \"randpm\\\\n27\\\\n2\\\\n\", \"random\\\\n5\\\\n2\\\\n\", \"5\\\\n<span class=\\\\\"tex-span\\\\\"></span>\\\\nblack\\\\n<span class=\\\\\"tex-span\\\\\"></span>\\\\nblack\\\\n<span class=\\\\\"tex-span\\\\\"></span>\\\\nwhite\\\\n<span class=\\\\\"tex-span\\\\\"></span>\\\\nwhite\\\\n<span class=\\\\\"tex-span\\\\\"></span>\\\\nblack\\\\n\"], \"outputs\": [\"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n0 0 1000000000 2\\\\n\", \"0 1\\\\n500000000 1\\\\n250000000 1\\\\n125000000 1\\\\n187500000 1\\\\n125000000 0 187500000 2\\\\n\"]}',\n", " 'difficulty': 'HARD',\n", " 'raw_tags': \"['interactive', 'binary search', 'geometry', 'constructive algorithms']\",\n", " 'name': None,\n", " 'source': 'codeforces',\n", " 'tags': \"['Geometry', 'Sorting', 'Constructive algorithms']\",\n", " 'skill_types': \"['Sorting']\",\n", " 'url': 'https://codeforces.com/problemset/problem/1063/C',\n", " 'Expected Auxiliary Space': None,\n", " 'time_limit': '2.0 seconds',\n", " 'date': None,\n", " 'picture_num': None,\n", " 'memory_limit': '256.0 megabytes',\n", " 'Expected Time Complexity': None,\n", " 'answer': \"['0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n0 0 1000000000 2\\\\n', '0 1\\\\n500000000 1\\\\n250000000 1\\\\n125000000 1\\\\n187500000 1\\\\n125000000 0 187500000 2\\\\n']\"}"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["test = TACO_train.map(lambda x: {\n", "    \"prompt\" : [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": x[\"prompt\"]},\n", "    ],\n", "    \"answer\": str(json.loads(x[\"input_output\"])['outputs'])\n", "})\n", "test[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.\n", "/opt/homebrew/anaconda3/envs/competitiveLLM/lib/python3.13/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"ename": "TypeError", "evalue": "argument of type 'NoneType' is not iterable", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[22]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      1\u001b[39m trainer = GRPOTrainer(\n\u001b[32m      2\u001b[39m     model=model,\n\u001b[32m      3\u001b[39m     reward_funcs=reward_check,\n\u001b[32m   (...)\u001b[39m\u001b[32m      6\u001b[39m     eval_dataset=[TACO_valid[\u001b[32m0\u001b[39m]]\n\u001b[32m      7\u001b[39m )\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[43mtrainer\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtrain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/anaconda3/envs/competitiveLLM/lib/python3.13/site-packages/transformers/trainer.py:2240\u001b[39m, in \u001b[36mTrainer.train\u001b[39m\u001b[34m(self, resume_from_checkpoint, trial, ignore_keys_for_eval, **kwargs)\u001b[39m\n\u001b[32m   2238\u001b[39m         hf_hub_utils.enable_progress_bars()\n\u001b[32m   2239\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m2240\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43minner_training_loop\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2241\u001b[39m \u001b[43m        \u001b[49m\u001b[43margs\u001b[49m\u001b[43m=\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2242\u001b[39m \u001b[43m        \u001b[49m\u001b[43mresume_from_checkpoint\u001b[49m\u001b[43m=\u001b[49m\u001b[43mresume_from_checkpoint\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2243\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtrial\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtrial\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2244\u001b[39m \u001b[43m        \u001b[49m\u001b[43mignore_keys_for_eval\u001b[49m\u001b[43m=\u001b[49m\u001b[43mignore_keys_for_eval\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2245\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/anaconda3/envs/competitiveLLM/lib/python3.13/site-packages/transformers/trainer.py:2509\u001b[39m, in \u001b[36mTrainer._inner_training_loop\u001b[39m\u001b[34m(self, batch_size, args, resume_from_checkpoint, trial, ignore_keys_for_eval)\u001b[39m\n\u001b[32m   2507\u001b[39m update_step += \u001b[32m1\u001b[39m\n\u001b[32m   2508\u001b[39m num_batches = args.gradient_accumulation_steps \u001b[38;5;28;01mif\u001b[39;00m update_step != (total_updates - \u001b[32m1\u001b[39m) \u001b[38;5;28;01melse\u001b[39;00m remainder\n\u001b[32m-> \u001b[39m\u001b[32m2509\u001b[39m batch_samples, num_items_in_batch = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mget_batch_samples\u001b[49m\u001b[43m(\u001b[49m\u001b[43mepoch_iterator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnum_batches\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdevice\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2510\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i, inputs \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(batch_samples):\n\u001b[32m   2511\u001b[39m     step += \u001b[32m1\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/anaconda3/envs/competitiveLLM/lib/python3.13/site-packages/transformers/trainer.py:5269\u001b[39m, in \u001b[36mTrainer.get_batch_samples\u001b[39m\u001b[34m(self, epoch_iterator, num_batches, device)\u001b[39m\n\u001b[32m   5264\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m:\n\u001b[32m   5265\u001b[39m         \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[32m   5267\u001b[39m count_num_items_in_batch = (\n\u001b[32m   5268\u001b[39m     \u001b[38;5;28mlen\u001b[39m(batch_samples) > \u001b[32m0\u001b[39m\n\u001b[32m-> \u001b[39m\u001b[32m5269\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[33;43m\"\u001b[39;49m\u001b[33;43mlabels\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mbatch_samples\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[32m   5270\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m (\n\u001b[32m   5271\u001b[39m         \u001b[38;5;66;03m# num_items_in_batch is passed to model forward\u001b[39;00m\n\u001b[32m   5272\u001b[39m         \u001b[38;5;66;03m# https://github.com/huggingface/transformers/blob/v4.49.0/src/transformers/trainer.py#L3757\u001b[39;00m\n\u001b[32m   5273\u001b[39m         \u001b[38;5;28mself\u001b[39m.model_accepts_loss_kwargs\n\u001b[32m   5274\u001b[39m         \u001b[38;5;66;03m# num_items_in_batch is passed to compute_loss_func\u001b[39;00m\n\u001b[32m   5275\u001b[39m         \u001b[38;5;66;03m# https://github.com/huggingface/transformers/blob/v4.49.0/src/transformers/trainer.py#L3773\u001b[39;00m\n\u001b[32m   5276\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m.compute_loss_func \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   5277\u001b[39m         \u001b[38;5;66;03m# num_items_in_batch is also verified if (self.model_accepts_loss_kwargs or self.compute_loss_func)\u001b[39;00m\n\u001b[32m   5278\u001b[39m         \u001b[38;5;66;03m# https://github.com/huggingface/transformers/blob/v4.49.0/src/transformers/trainer.py#L3790\u001b[39;00m\n\u001b[32m   5279\u001b[39m     )\n\u001b[32m   5280\u001b[39m )\n\u001b[32m   5282\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m count_num_items_in_batch:\n\u001b[32m   5283\u001b[39m     \u001b[38;5;66;03m# For now we don't support object detection\u001b[39;00m\n\u001b[32m   5284\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[31mTypeError\u001b[39m: argument of type 'NoneType' is not iterable"]}], "source": ["trainer = GRPOTrainer(\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    reward_funcs=reward_check,\n", "    args=training_args,\n", "    train_dataset=[TACO_train[0]],\n", "    eval_dataset=[TACO_valid[0]]\n", ")\n", "trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}