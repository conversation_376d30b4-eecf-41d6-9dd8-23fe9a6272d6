{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0J2zZQKUizbE", "outputId": "d8f2dff9-574e-4ba2-b10f-314275ec00a1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["d\n"]}], "source": ["# Use OpenRouter key (use secrets from <PERSON><PERSON>/<PERSON>ggle)\n", "def get_secret(name):\n", "    try:\n", "        from google.colab import userdata\n", "        return userdata.get(name)\n", "    except:\n", "      print(\"d\")\n", "\n", "OPENROUTER_API_KEY = get_secret('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')\n", "model = \"qwen/qwen3-8b:free\"\n", "question = \"What is the meaning of life?\""]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hT2hty_8go7J", "outputId": "6a4bcd67-9c50-48b2-cb78-4739615f7480"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The question \"What is the meaning of life?\" is one of the most profound and enduring inquiries in human thought, and its answer varies across perspectives, cultures, and individuals. Here are some key viewpoints to consider:\n", "\n", "### 1. **Philosophical Perspectives**  \n", "   - **Existentialism**: Philosophers like <PERSON><PERSON><PERSON> and <PERSON> argue that life has no inherent meaning. Instead, individuals are free to create their own purpose through choices, actions, and personal values.  \n", "   - **Absurdism**: <PERSON><PERSON> suggests that the search for meaning in a chaotic universe is inherently absurd, but one can find fulfillment by embracing life's contradictions and living authentically.  \n", "   - **Stoicism & Hedonism**: Some philosophies focus on living virtuously (Stoicism) or pursuing pleasure and fulfillment (Hedonism) as the essence of a meaningful life.  \n", "\n", "### 2. **Religious & Spiritual Views**  \n", "   Many faith traditions offer answers rooted in divine purpose:  \n", "   - **Abrahamic Religions**: Christianity, Islam, and Judaism often frame life’s meaning as serving a higher power, following moral laws, or fulfilling a divine plan.  \n", "   - **Eastern Religions**: Buddhism emphasizes ending suffering (dukkha) through enlightenment and detachment, while Hinduism often ties meaning to dharma (duty) and moksha (liberation).  \n", "   - **Pantheism/Unitarianism**: Some spiritual paths view life as a part of a greater, unified existence, where meaning arises from connection to the universe or collective consciousness.  \n", "\n", "### 3. **Scientific Perspectives**  \n", "   - **Biological/Naturalistic**: From an evolutionary standpoint, life’s purpose might be seen as survival, reproduction, and the continuation of species.  \n", "   - **Cosmic Perspective**: Scientists like <PERSON> or <PERSON> sometimes frame meaning as a byproduct of the universe’s processes, emphasizing curiosity, exploration, and understanding of existence.  \n", "\n", "### 4. **Psychological & Personal Views**  \n", "   - **<PERSON>’s Logotherapy**: He believed meaning is found through love, work, suffering, or creativity, even in the face of adversity.  \n", "   - **Self-Determination Theory**: Modern psychology suggests meaning emerges from autonomy, competence, and relatedness—relationships, personal growth, and contributing to something larger than oneself.  \n", "\n", "### 5. **Cultural & Societal Influences**  \n", "   - Collective values, traditions, and societal roles often shape how cultures define purpose. For some, meaning lies in community, family, or service; for others, in personal achievements or artistic expression.  \n", "\n", "### 6. **Modern & Individualistic Approaches**  \n", "   - Many people today see meaning as fluid, evolving with personal goals, passions, or experiences. It might involve seeking happiness, personal fulfillment, or contributing to humanity’s progress.  \n", "\n", "### Key Takeaway:  \n", "The meaning of life is **not a fixed answer** but a **dynamic exploration**. It can be found in relationships, purpose, creativity, or even in questioning itself. For some, it’s a deeply spiritual quest; for others, it’s about making the most of the time they have. Ultimately, the search for meaning is a personal journey, shaped by your values, beliefs, and experiences.  \n", "\n", "Would you like to explore a specific angle further? 😊\n"]}], "source": ["# Use OpenAI library as a base\n", "from openai import OpenAI\n", "\n", "client = OpenAI(\n", "    base_url=\"https://openrouter.ai/api/v1\",\n", "    api_key='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'\n", ")\n", "\n", "completion = client.chat.completions.create(\n", "    model=model,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": question\n", "        }\n", "    ]\n", ")\n", "\n", "print(completion.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Wm73oXjgi5_p", "outputId": "b084b510-6c90-4367-9a9f-e73257ac9d1e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The question of the meaning of life is one of the most profound and enduring inquiries in human thought, spanning philosophy, religion, science, and personal reflection. While there is no universally agreed-upon answer, various perspectives offer insights into how different people and traditions approach this question. Here’s an organized overview of these viewpoints:\n", "\n", "---\n", "\n", "### **1. Religious and Spiritual Perspectives**\n", "Religious traditions often propose a transcendent or divine purpose for life:\n", "- **Abrahamic Religions (e.g., Christianity, Islam, Judaism):**  \n", "  Life is often seen as a divine gift, with a purpose tied to serving God, following moral laws, and achieving spiritual salvation or union with the divine. For example:\n", "  - **Christianity:** Life's meaning is to know and serve God, live virtuously, and seek eternal life.\n", "  - **Islam:** Life's purpose is to worship <PERSON>, live by His guidance, and prepare for the afterlife.\n", "  - **Judaism:** Emphasizes fulfilling God’s commandments, observing traditions, and living a life of ethical responsibility.\n", "- **Eastern Religions (e.g., Buddhism, Hinduism):**  \n", "  - **Buddhism:** Focuses on ending suffering (dukkha) through enlightenment (nirvana) and detachment from desires.\n", "  - **Hinduism:** Suggests the ultimate purpose is *moksha* (liberation from the cycle of rebirth) or fulfilling *dharma* (one’s duty in life).\n", "\n", "---\n", "\n", "### **2. Philosophical Perspectives**\n", "Philosophers have explored the meaning of life from diverse angles:\n", "- **Existentialism (e.g., <PERSON><PERSON><PERSON>, Camus):**  \n", "  Life has no inherent meaning; individuals must create their own purpose through choices, actions, and responsibility. <PERSON><PERSON>, for instance, argues that meaning is found in embracing life’s absurdity and living authentically.\n", "- **Stoicism:**  \n", "  Meaning lies in living virtuously, aligning with nature, and accepting what cannot be changed. It emphasizes inner resilience and rationality.\n", "- **<PERSON><PERSON><PERSON><PERSON>:**  \n", "  The purpose of life is to achieve *eudaimonia* (\"flourishing\" or \"true happiness\") through the cultivation of virtue and rational activity.\n", "- **Utilitarianism (e.g., Bentham, Mill):**  \n", "  Life’s meaning is tied to maximizing happiness and minimizing suffering for the greatest number.\n", "\n", "---\n", "\n", "### **3. Scientific Perspectives**\n", "Science approaches the question from an empirical standpoint:\n", "- **Evolutionary Biology:**  \n", "  Life may be seen as a product of natural selection, with purpose tied to survival, reproduction, and genetic continuity. However, this is often debated as being *biologically* objective rather than *existentially* fulfilling.\n", "- **Cosmology:**  \n", "  The universe appears indifferent, with no evidence of inherent purpose. This has led some to argue that meaning must be self-created, given the absence of a preordained plan.\n", "- **Psychology:**  \n", "  Researchers like <PERSON> (author of *Man’s Search for Meaning*) suggest that meaning can emerge from personal goals, relationships, work, or even suffering. The concept of \"logotherapy\" posits that the search for meaning is central to human motivation.\n", "\n", "---\n", "\n", "### **4. Cultural and Personal Perspectives**\n", "Cultural values and individual experiences shape meaning:\n", "- **Collectivist Cultures:** Often emphasize community, family, and social harmony as central to life’s purpose.\n", "- **Individualist Cultures:** May prioritize personal aspirations, self-actualization, or autonomy.\n", "- **Personal Meaning:** Many find purpose in relationships, creativity, contribution to others, or the pursuit of knowledge, love, or happiness. For example:\n", "  - Some seek meaning through career or achievements.\n", "  - Others through art, spirituality, or altruism.\n", "  - Still others through exploration, curiosity, or philosophical inquiry.\n", "\n", "---\n", "\n", "### **5. The Search for Meaning Itself**\n", "- **Absurdism (Camus):**  \n", "  Life is inherently meaningless, but the struggle to find meaning in an indifferent universe is what gives life its value.\n", "- **Existentialist Search:**  \n", "  The journey of creating meaning, facing freedom and choice, and defining one’s values is itself the essence of life.\n", "- **Narrative Theory:**  \n", "  Humans construct personal narratives to give life coherence and purpose, even if these stories are not objectively \"true.\"\n", "\n", "---\n", "\n", "### **Key Themes Across Perspectives**\n", "- **Subjectivity vs. Objectivity:**  \n", "  Is meaning inherent (universal) or self-constructed (individual)? Many argue it is both: some believe in a higher purpose, while others see meaning as a human invention.\n", "- **Purpose as a Construct:**  \n", "  A common thread is that meaning is often shaped by what individuals or societies value—whether it’s faith, knowledge, love, or legacy.\n", "- **The Moment vs. the Journey:**  \n", "  Some find meaning in the present (e.g., mindfulness and gratitude), while others seek it through long-term goals or spiritual ascent.\n", "\n", "---\n", "\n", "### **A Summary**\n", "The meaning of life is not a single answer but a multifaceted question that invites exploration. It can be:\n", "- **A religious calling** (e.g., devotion, service, salvation).  \n", "- **A philosophical pursuit** (e.g., self-creation, virtue, happiness).  \n", "- **A scientific inquiry** (e.g., understanding existence, adapting to the environment).  \n", "- **A personal narrative** (e.g., love, creativity, legacy).  \n", "- **A search for purpose** itself, even in the absence of a predefined answer.  \n", "\n", "Ultimately, many suggest that the search for meaning is as important as the meaning itself, and that life’s significance is often discovered through engagement with the world, relationships, and the pursuit of what matters most to the individual. As poet <PERSON><PERSON><PERSON> wrote, *\"Live the questions now, and perhaps you will gradually gradually… become the answer.\"*\n"]}], "source": ["# Directly call API via reqiests\n", "import requests\n", "import json\n", "\n", "try:\n", "    response = requests.post(\n", "        url=\"https://openrouter.ai/api/v1/chat/completions\",\n", "        headers={\n", "            \"Authorization\": \"Bearer \" + '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',\n", "        },\n", "        data=json.dumps({\n", "            \"model\": model,\n", "            \"messages\": [\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": \"What is the meaning of life?\"\n", "                }\n", "            ]\n", "        })\n", "    )\n", "    response.raise_for_status()\n", "except requests.exceptions.RequestException as e:\n", "    print(f'HTTP error occurred: {e}')\n", "    print(f'Status Code: {response.status_code}')\n", "    print(f'Response Headers: {response.headers}')\n", "    print(f'Response Body: {response.text}')\n", "\n", "print(json.loads(response.text)['choices'][0]['message']['content'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KkUj_EDGkYJv", "outputId": "3a81432e-282c-43d9-c63b-f71137cd90e8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The question of the **meaning of life** is one of humanity's oldest and most profound inquiries, and it has no single, universally accepted answer. Itâs a deeply personal and subjective question, often explored through philosophy, religion, science, and individual experiences. Here are some key perspectives that people and thinkers have proposed over time:\n", "\n", "---\n", "\n", "### 1. **Philosophical Perspectives**  \n", "   - **Existentialism**: Philosophers like <PERSON><PERSON><PERSON> and <PERSON> argue that life has **no inherent meaning**âit is up to each individual to create their own purpose through choices, actions, and responsibility.  \n", "   - **Stoicism**: Focuses on living in harmony with nature, embracing virtues like wisdom, courage, and resilience, and finding meaning in self-mastery and ethical living.  \n", "   - **Utilitarianism**: Suggests meaning arises from maximizing well-being and happiness for the greatest number of people.  \n", "   - **Nihilism**: Posits that life may lack intrinsic meaning, though some nihilists argue that this realization can lead to freedom or authenticity.  \n", "\n", "---\n", "\n", "### 2. **Religious and Spiritual Views**  \n", "   - Many religious traditions propose that lifeâs purpose is tied to a divine plan, spiritual growth, or service to a higher power. For example:  \n", "     - **Abrahamic religions** (Judaism, Christianity, Islam): Often emphasize living according to divine will, moral conduct, and preparing for an afterlife.  \n", "     - **Eastern philosophies** (Buddhism, Hinduism): Highlight liberation from suffering (*moksha* or *nirvana*), self-realization, or alignment with cosmic order (*dharma*).  \n", "     - **Pantheism/Animism**: Suggest that meaning is found in the interconnectedness of all life and the divine presence in the natural world.  \n", "\n", "---\n", "\n", "### 3. **Scientific and Naturalistic Approaches**  \n", "   - From a biological standpoint, some argue lifeâs \"meaning\" is tied to **survival and reproduction** (evolutionary purpose).  \n", "   - Others find meaning in **curiosity, exploration, and understanding the universe**âdriven by the human brainâs innate desire to seek patterns and knowledge.  \n", "   - The **search for purpose** itself is sometimes seen as a product of the human mind, rather than an objective reality.  \n", "\n", "---\n", "\n", "### 4. **Psychological and Personal Views**  \n", "   - Psychologists and therapists often emphasize **finding meaning through relationships, goals, and personal values**. <PERSON> *Manâs Search for Meaning* suggests that even in suffering, people can find purpose by choosing how to respond.  \n", "   - For many, meaning arises from **contributing to others**, pursuing passions, or experiencing joy, love, and creativity.  \n", "\n", "---\n", "\n", "### 5. **Cultural and Societal Influences**  \n", "   - Different cultures and communities may define meaning through shared values, traditions, or collective achievements.  \n", "   - Some find purpose in **art, creativity, or legacy**, while others focus on **community, family, or societal progress**.  \n", "\n", "---\n", "\n", "### 6. **The Absence of a Fixed Answer**  \n", "   - The question may be **unanswerable** in a definitive sense, as it often depends on individual beliefs, experiences, and existential reflections.  \n", "   - This ambiguity can lead to **existential questions** about free will, purpose, and the nature of consciousness.  \n", "\n", "---\n", "\n", "### Final Thoughts  \n", "Ultimately, the meaning of life is a **journey of discovery** rather than a single destination. It might involve:  \n", "- **Self-reflection**: Exploring what resonates with your values and aspirations.  \n", "- **Relationships**: Building connections with others and contributing to their well-being.  \n", "- **Growth**: Seeking to understand yourself, the world, or something beyond the material.  \n", "- **Acceptance**: Embracing lifeâs uncertainties and finding joy in the present.  \n", "\n", "As the writer **<PERSON> once said, \"Follow your bliss and doors will open where you didn't know they would.\" The answer may lie in what you *choose* to value and pursue. What do you think gives your life meaning? ð±â¨"]}], "source": ["# Stream response\n", "url = \"https://openrouter.ai/api/v1/chat/completions\"\n", "headers = {\n", "  \"Authorization\": \"Bearer \" + '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',\n", "  \"Content-Type\": \"application/json\"\n", "}\n", "\n", "payload = {\n", "  \"model\": model,\n", "  \"messages\": [\n", "    {\n", "      \"role\": \"user\",\n", "      \"content\": question\n", "    }\n", "  ],\n", "  \"stream\": True\n", "}\n", "\n", "buffer = \"\"\n", "with requests.post(url, headers=headers, json=payload, stream=True) as r:\n", "  for chunk in r.iter_content(chunk_size=1024, decode_unicode=True):\n", "    buffer += chunk\n", "    while True:\n", "      try:\n", "        # Find the next complete SSE line\n", "        line_end = buffer.find('\\n')\n", "        if line_end == -1:\n", "          break\n", "\n", "        line = buffer[:line_end].strip()\n", "        buffer = buffer[line_end + 1:]\n", "\n", "        if line.startswith('data: '):\n", "          data = line[6:]\n", "          if data == '[DONE]':\n", "            break\n", "\n", "          try:\n", "            data_obj = json.loads(data)\n", "            content = data_obj[\"choices\"][0][\"delta\"].get(\"content\")\n", "            if content:\n", "              print(content, end=\"\", flush=True)\n", "          except json.JSONDecodeError:\n", "            pass\n", "      except Exception:\n", "        break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gvomt4KtelPw"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "bus", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.4"}}, "nbformat": 4, "nbformat_minor": 0}